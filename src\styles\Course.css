/* Course Page Styles - Consolidated CSS */
.courses-page-container {
    padding: 0; /* No padding to start from top */
    background-color: #FFFBF3; /* Match main content background */
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    flex-direction: column;
  }

  /* Header Card - Flush with top, borders on left/right/bottom only */
  .courses-header-card {
    background-color: #FFF8EB; /* Match sidebar background color */
    border: 1px solid #e0e0e0;
    border-top: none; /* Hide top border to make it flush with top */
    border-radius: 0 0 12px 12px; /* Only bottom corners rounded */
    box-shadow: none;
    padding: 24px 40px;
    text-align: center;
    margin-top: 0; /* Ensure no top margin */
  }

  .courses-page-title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  /* Controls Row - Breadcrumb left, Add button right */
  .courses-controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 32px 0px;


  }

  .courses-breadcrumb {
    flex: 1;
  }

  .courses-breadcrumb span {
    font-size: 16px;
    color: #6b7280;
    text-decoration: underline;
    font-weight: 400;
  }

  .courses-add-btn {
    background-color: #1f2937; /* Darker background to match design */
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .courses-add-btn:hover {
    background-color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* DataTable Wrapper Styling */
  .courses-datatable-wrapper {
    flex: 1;
  }

  /* DataTable Integration Styles */
  .courses-datatable .data-table-page {
    padding: 0;
    gap: 0;
    background: transparent;
  }

  .courses-datatable .data-table-header {
    display: none; /* Hide default DataTable header since we have custom header */
  }

  .courses-datatable .data-table-content {
    width: 100%;
  }

  /* Table Container */
  .courses-table-container {
    background-color: #FFF8EB; /* Match sidebar background color */
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 0;
  }

  .courses-datatable .data-table-wrapper {
    margin-top: 0;
    border: none;
    border-radius: 12px;
    overflow: hidden;
  }

  /* Table Base Styles */
  .courses-table {
    width: 100%;
    border-collapse: collapse;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    border: none;
    font-size: 14px;
  }



  .courses-table thead {
    background-color: #f8f9fa;
  }

  .courses-table th {
    padding: 16px 20px;
    font-weight: 600;
    color: #374151; /* Slightly darker for better contrast */
    font-size: 14px;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
  }

  .courses-table th:last-child {
    text-align: center;
  }

  .courses-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    background-color: #FFF8EB; /* Match sidebar background color */
  }

  .courses-table tbody tr:hover {
    background-color: #f9fafb; /* Slightly lighter hover effect */
    cursor: pointer;
  }

  .courses-table tbody tr:last-child {
    border-bottom: none;
  }

  .courses-table td {
    padding: 16px 20px;
    color: #374151; /* Consistent with header color */
    border-right: none;
    vertical-align: middle;
    font-size: 14px;
  }

  /* Column-specific styles */
  .course-name-cell {
    font-weight: 500;
    color: #1f2937; /* Darker for course names */
  }

  .course-price-cell {
    font-weight: 500;
    color: #374151;
  }

  .course-duration-cell {
    color: #6b7280; /* Lighter gray for duration */
  }

  .course-edit-cell {
    text-align: center;
    vertical-align: middle;
  }

  .course-edit-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    color: #6b7280; /* Consistent gray color */
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto; /* Center the button within the cell */
  }

  .course-edit-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  .course-edit-btn svg {
    width: 16px;
    height: 16px;
  }

  /* DataTable State Styles */
  .courses-datatable .loading-container,
  .courses-datatable .error-container,
  .courses-datatable .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    text-align: center;
    background-color: #FFF8EB; /* Match sidebar background color */
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    margin: 0;
  }

  .courses-datatable .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e0e0e0;
    border-top-color: #1f2937;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .courses-datatable .retry-button {
    background-color: #1f2937;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 16px;
  }

  .courses-datatable .retry-button:hover {
    background-color: #374151;
  }

  .courses-datatable .text-error {
    color: #D32F2F;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .courses-page-container {
      padding: 0; /* Maintain no top padding */
    }

    .courses-header-card {
      padding: 20px 30px;
    }

    .courses-controls-row {
      padding: 16px 30px;
    }



    .courses-page-title {
      font-size: 28px;
    }

    .courses-add-btn {
      padding: 10px 20px;
      font-size: 13px;
    }
  }

  @media (max-width: 768px) {
    .courses-page-container {
      padding: 0; /* Maintain no top padding */
    }

    .courses-header-card {
      padding: 16px 20px;
    }

    .courses-controls-row {
      padding: 16px 20px;
    }



    .courses-page-title {
      font-size: 24px;
    }

    .courses-controls-row {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .courses-breadcrumb {
      text-align: center;
    }

    .courses-add-btn {
      width: 100%;
      justify-content: center;
    }

    .courses-datatable .data-table-wrapper {
      overflow-x: auto;
    }

    .courses-table {
      min-width: 500px;
    }

    .courses-table th,
    .courses-table td {
      padding: 12px 16px;
    }
  }

  @media (max-width: 480px) {
    .courses-page-container {
      padding: 0; /* Maintain no top padding */
    }

    .courses-header-card {
      padding: 12px 16px;
    }

    .courses-controls-row {
      padding: 12px 16px;
    }



    .courses-page-title {
      font-size: 20px;
    }

    .courses-add-btn {
      padding: 10px 16px;
      font-size: 12px;
    }

    .courses-breadcrumb span {
      font-size: 14px;
    }

    .courses-table {
      font-size: 12px;
      min-width: 450px;
    }

    .courses-table th,
    .courses-table td {
      padding: 10px 12px;
    }

    .course-edit-btn {
      padding: 6px;
    }

    .course-edit-btn svg {
      width: 14px;
      height: 14px;
    }
  }