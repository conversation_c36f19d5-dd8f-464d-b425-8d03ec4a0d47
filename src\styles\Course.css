/* Course Page Styles - Using DataTable */
.courses-page-container {
    padding: 0; /* No padding to start from top */
    background-color: #FFFBF3; /* Match main content background */
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    flex-direction: column;
  }

  /* Header Card - Flush with top, borders on left/right/bottom only */
  .courses-header-card {
    background-color: #FFF8EB; /* Match sidebar background color */
    border: 1px solid #e0e0e0;
    border-top: none; /* Hide top border to make it flush with top */
    border-radius: 0 0 12px 12px; /* Only bottom corners rounded */
    box-shadow: none;
    padding: 24px 40px;
    text-align: center;
    margin-top: 0; /* Ensure no top margin */
  }

  .courses-page-title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  /* Controls Row - Breadcrumb left, Add button right */
  .courses-controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 32px 0px;


  }

  .courses-breadcrumb {
    flex: 1;
  }

  .courses-breadcrumb span {
    font-size: 16px;
    color: #6b7280;
    text-decoration: underline;
    font-weight: 400;
  }

  .courses-add-btn {
    background-color: #1f2937; /* Darker background to match design */
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .courses-add-btn:hover {
    background-color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* DataTable Wrapper Styling */
  .courses-datatable-wrapper {
    flex: 1;
  }

  /* Override DataTable default styles to match image design */
  .courses-datatable .data-table-page {
    padding: 0;
    gap: 0;
    background: transparent;
  }

  .courses-datatable .data-table-header {
    display: none; /* Hide default DataTable header since we have custom header */
  }

  /* Custom Table Container */
  .courses-table-container {
    background-color: #FFF8EB !important; /* Match sidebar background color */
    border-radius: 12px !important;
    border: 1px solid #e0e0e0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    padding: 0 !important;
  }

  .courses-datatable .data-table-wrapper {
    margin-top: 0 !important;
    border: none !important;
    border-radius: 12px !important;
    overflow: hidden;
  }

  /* Custom Table Styles */
  .courses-table {
    border: none !important;
    font-size: 14px !important;
  }



  .courses-table th {
    padding: 16px 20px !important;
    font-weight: 600 !important;
    color: #374151 !important; /* Slightly darker for better contrast */
    font-size: 14px !important;
    border-right: none !important;
    border-bottom: 1px solid #e0e0e0 !important;
    text-align: left !important;
    vertical-align: middle !important;
  }

  .courses-table th:last-child {
    text-align: center !important;
  }

  .courses-table tbody tr {
    border-bottom: 1px solid #f0f0f0 !important;
  }



  .courses-table tbody tr:last-child {
    border-bottom: none !important;
  }

  .courses-table td {
    padding: 16px 20px !important;
    color: #374151 !important; /* Consistent with header color */
    border-right: none !important;
    vertical-align: middle;
    font-size: 14px !important;
  }

  /* Column-specific styles */
  .course-name-cell {
    font-weight: 500 !important;
    color: #1f2937 !important; /* Darker for course names */
  }

  .course-price-cell {
    font-weight: 500 !important;
    color: #374151 !important;
  }

  .course-duration-cell {
    color: #6b7280 !important; /* Lighter gray for duration */
  }

  .course-edit-cell {
    text-align: center !important;
    vertical-align: middle !important;
  }

  .course-edit-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    color: #6b7280; /* Consistent gray color */
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto; /* Center the button within the cell */
  }

  .course-edit-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  .course-edit-btn svg {
    width: 16px;
    height: 16px;
  }

  /* Override DataTable loading/error/empty states */
  .courses-datatable .loading-container,
  .courses-datatable .error-container,
  .courses-datatable .empty-container {
    background-color: #FFF8EB; /* Match sidebar background color */
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    margin: 0;
  }

  .courses-datatable .loading-spinner {
    border-top-color: #1f2937 !important;
  }

  .courses-datatable .retry-button {
    background-color: #1f2937 !important;
    color: white !important;
    border-radius: 8px !important;
    padding: 12px 24px !important;
    font-weight: 500 !important;
  }

  .courses-datatable .retry-button:hover {
    background-color: #374151 !important;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .courses-page-container {
      padding: 0; /* Maintain no top padding */
    }

    .courses-header-card {
      padding: 20px 30px;
    }

    .courses-controls-row {
      padding: 16px 30px;
    }



    .courses-page-title {
      font-size: 28px;
    }

    .courses-add-btn {
      padding: 10px 20px;
      font-size: 13px;
    }
  }

  @media (max-width: 768px) {
    .courses-page-container {
      padding: 0; /* Maintain no top padding */
    }

    .courses-header-card {
      padding: 16px 20px;
    }

    .courses-controls-row {
      padding: 16px 20px;
    }



    .courses-page-title {
      font-size: 24px;
    }

    .courses-controls-row {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .courses-breadcrumb {
      text-align: center;
    }

    .courses-add-btn {
      width: 100%;
      justify-content: center;
    }

    .courses-datatable .data-table-wrapper {
      overflow-x: auto;
    }

    .courses-table {
      min-width: 500px !important;
    }

    .courses-table th,
    .courses-table td {
      padding: 12px 16px !important;
    }
  }

  @media (max-width: 480px) {
    .courses-page-container {
      padding: 0; /* Maintain no top padding */
    }

    .courses-header-card {
      padding: 12px 16px;
    }

    .courses-controls-row {
      padding: 12px 16px;
    }



    .courses-page-title {
      font-size: 20px;
    }

    .courses-add-btn {
      padding: 10px 16px;
      font-size: 12px;
    }

    .courses-breadcrumb span {
      font-size: 14px;
    }

    .courses-table {
      font-size: 12px !important;
      min-width: 450px !important;
    }

    .courses-table th,
    .courses-table td {
      padding: 10px 12px !important;
    }

    .course-edit-btn {
      padding: 6px;
    }

    .course-edit-btn svg {
      width: 14px;
      height: 14px;
    }
  }