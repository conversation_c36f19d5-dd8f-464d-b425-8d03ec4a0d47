/* Course Page Styles - Using DataTable */
.courses-page-container {
    padding: 8px 40px 20px; /* Very small top padding, normal sides and bottom */
    background-color: #faf9f6;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  /* Header Card - Separate section at top */
  .courses-header-card {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 24px 32px;
    text-align: center;
  }
  
  .courses-page-title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
  
  /* Controls Row - Breadcrumb left, Add button right */
  .courses-controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
  }
  
  .courses-breadcrumb {
    flex: 1;
  }
  
  .courses-breadcrumb span {
    font-size: 16px;
    color: #666;
    text-decoration: underline;
  }
  
  .courses-add-btn {
    background-color: #333;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }
  
  .courses-add-btn:hover {
    background-color: #555;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  /* DataTable Wrapper Styling */
  .courses-datatable-wrapper {
    flex: 1;
  }
  
  /* Override DataTable default styles to match image design */
  .courses-datatable .data-table-page {
    padding: 0;
    gap: 0;
    background: transparent;
  }
  
  .courses-datatable .data-table-header {
    display: none; /* Hide default DataTable header since we have custom header */
  }
  
  /* Custom Table Container */
  .courses-table-container {
    background-color: white !important;
    border-radius: 12px !important;
    border: 1px solid #e0e0e0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    padding: 0 !important;
  }
  
  .courses-datatable .data-table-wrapper {
    margin-top: 0 !important;
    border: none !important;
    border-radius: 12px !important;
    overflow: hidden;
  }
  
  /* Custom Table Styles */
  .courses-table {
    border: none !important;
    font-size: 14px !important;
  }
  
  .courses-table thead {
    background-color: #f8f9fa !important;
  }
  
  .courses-table th {
    padding: 16px 20px !important;
    font-weight: 600 !important;
    color: #333 !important;
    font-size: 14px !important;
    border-right: none !important;
    border-bottom: 1px solid #e0e0e0 !important;
  }
  
  .courses-table th:last-child {
    text-align: center;
  }
  
  .courses-table tbody tr {
    border-bottom: 1px solid #f0f0f0 !important;
  }
  
  .courses-table tbody tr:hover {
    background-color: #fafafa !important;
  }
  
  .courses-table tbody tr:last-child {
    border-bottom: none !important;
  }
  
  .courses-table td {
    padding: 16px 20px !important;
    color: #333 !important;
    border-right: none !important;
    vertical-align: middle;
  }
  
  /* Column-specific styles */
  .course-name-cell {
    font-weight: 500 !important;
    color: #333 !important;
  }
  
  .course-price-cell {
    font-weight: 500 !important;
    color: #333 !important;
  }
  
  .course-duration-cell {
    color: #666 !important;
  }
  
  .course-edit-cell {
    text-align: center !important;
  }
  
  .course-edit-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    color: #666;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  .course-edit-btn:hover {
    background-color: #f0f0f0;
    color: #333;
  }
  
  .course-edit-btn svg {
    width: 16px;
    height: 16px;
  }
  
  /* Override DataTable loading/error/empty states */
  .courses-datatable .loading-container,
  .courses-datatable .error-container,
  .courses-datatable .empty-container {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    margin: 0;
  }
  
  .courses-datatable .loading-spinner {
    border-top-color: #333 !important;
  }
  
  .courses-datatable .retry-button {
    background-color: #333 !important;
    color: white !important;
  }
  
  .courses-datatable .retry-button:hover {
    background-color: #555 !important;
  }
  
  /* Responsive Design */
  @media (max-width: 1024px) {
    .courses-page-container {
      padding: 8px 30px 20px; /* Maintain small top padding */
      gap: 16px;
    }
    
    .courses-header-card {
      padding: 20px 24px;
    }
    
    .courses-page-title {
      font-size: 28px;
    }
    
    .courses-add-btn {
      padding: 10px 20px;
      font-size: 13px;
    }
  }
  
  @media (max-width: 768px) {
    .courses-page-container {
      padding: 8px 20px 16px; /* Maintain small top padding */
      gap: 16px;
    }
    
    .courses-header-card {
      padding: 16px 20px;
    }
    
    .courses-page-title {
      font-size: 24px;
    }
    
    .courses-controls-row {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }
    
    .courses-breadcrumb {
      text-align: center;
    }
    
    .courses-add-btn {
      width: 100%;
      justify-content: center;
    }
    
    .courses-datatable .data-table-wrapper {
      overflow-x: auto;
    }
    
    .courses-table {
      min-width: 500px !important;
    }
    
    .courses-table th,
    .courses-table td {
      padding: 12px 16px !important;
    }
  }
  
  @media (max-width: 480px) {
    .courses-page-container {
      padding: 6px 16px 12px; /* Even smaller top padding for mobile */
      gap: 12px;
    }
    
    .courses-header-card {
      padding: 12px 16px;
    }
    
    .courses-page-title {
      font-size: 20px;
    }
    
    .courses-add-btn {
      padding: 10px 16px;
      font-size: 12px;
    }
    
    .courses-breadcrumb span {
      font-size: 14px;
    }
    
    .courses-table {
      font-size: 12px !important;
      min-width: 450px !important;
    }
    
    .courses-table th,
    .courses-table td {
      padding: 10px 12px !important;
    }
    
    .course-edit-btn {
      padding: 6px;
    }
    
    .course-edit-btn svg {
      width: 14px;
      height: 14px;
    }
  }