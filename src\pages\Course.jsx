import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { coursesAPI } from '../services/api';
import SideNavBar from '../components/layout/SideNavBar';
import DataTable from '../components/ui/DataTable';
import '../styles/Course.css';

const Course = () => {
  const navigate = useNavigate();
  const { currentUser, logout } = useAuthStore();
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch courses when component loads
  useEffect(() => {
    const fetchCourses = async (retryCount = 0) => {
      try {
        setLoading(true);
        setError(null);
        console.log(`[Course] Fetching courses... (attempt ${retryCount + 1})`);

        if (retryCount === 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        const response = await coursesAPI.getAll({
          offset: 0,
          limit: 50
        });

        console.log('[Course] Courses fetched successfully:', response);

        const transformedCourses = response.data.map(course => ({
          id: course.id,
          courseName: course.title,
          price: course.offerPrice || course.originalPrice || 'XXXX',
          duration: '4 months', // Default duration as shown in image
          status: course.status,
          displayPicture: course.displayPicture,
          createdAt: course.createdAt,
          updatedAt: course.updatedAt
        }));

        setCourses(transformedCourses);

      } catch (error) {
        console.error(`[Course] Failed to fetch courses (attempt ${retryCount + 1}):`, error);

        if (error.response?.status === 401 && retryCount === 0) {
          console.log('[Course] 401 error on first attempt, retrying after delay...');
          setTimeout(() => {
            fetchCourses(1);
          }, 500);
          return;
        }

        setError(error.message || 'Failed to load courses');
        setCourses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  const handleTabChange = (tabId) => {
    console.log('[Course] Navigating to tab:', tabId);
    if (tabId === 'home') {
      navigate('/dashboard', { replace: true });
    } else if (tabId === 'courses') {
      navigate('/course', { replace: true });
    } else if (tabId === 'profile') {
      navigate('/profile', { replace: true });
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleCourseEdit = (course, event) => {
    if (event) event.stopPropagation();
    console.log('Course selected for editing:', course);
    // TODO: Navigate to course edit page
    // navigate(`/course/${course.id}/edit`);
  };

  const handleCourseClick = (course) => {
    console.log('Course clicked:', course);
    // For now, just trigger edit functionality
    handleCourseEdit(course);
  };

  const formatPrice = (price) => {
    if (!price) return 'XXXX';
    if (typeof price === 'string' && price.includes('₹')) return price;
    if (price === 'XXXX') return 'XXXX';
    return `₹${price}`;
  };

  // Define table columns configuration to match the image design
  const columns = [
    {
      key: 'courseName',
      header: 'Course Name',
      cellClassName: 'course-name-cell',
    },
    {
      key: 'price',
      header: 'Price',
      cellClassName: 'course-price-cell',
      formatter: formatPrice
    },
    {
      key: 'duration',
      header: 'Duration',
      cellClassName: 'course-duration-cell',
    },
    {
      key: 'edit',
      header: 'Edit',
      cellClassName: 'course-edit-cell',
      render: (_, course) => (
        <button 
          className="course-edit-btn"
          onClick={(e) => handleCourseEdit(course, e)}
          title="Edit course"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
            <path d="m18.5 2.5 a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
          </svg>
        </button>
      )
    }
  ];

  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab="courses"
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      
      <div className="dashboard-main-content">
        <div className="courses-page-container">
          {/* Header Card - Separate section */}
          <div className="courses-header-card">
            <h1 className="courses-page-title">Courses</h1>
          </div>

          {/* Breadcrumb and Add Button Row */}
          <div className="courses-controls-row">
            <div className="courses-breadcrumb">
              <span>List of courses</span>
            </div>
            <button className="courses-add-btn" onClick={() => navigate('/add-course')}>
              Add A New Course
            </button>
          </div>

          {/* DataTable with custom styling */}
          <div className="courses-datatable-wrapper">
            <DataTable
              data={courses}
              loading={loading}
              error={error}
              title="" // Remove default title since we have custom header
              columns={columns}
              onRowClick={handleCourseClick}
              onRetry={() => window.location.reload()}
              emptyMessage="No courses available at the moment."
              loadingMessage="Loading courses..."
              className="courses-datatable"
              tableClassName="courses-table"
              containerClassName="courses-table-container"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Course;