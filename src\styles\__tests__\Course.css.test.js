/**
 * CSS Consolidation Test
 * This test verifies that the Course page styling works correctly
 * after consolidating DataTable.css styles into Course.css
 */

import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import Course from '../../pages/Course';
import { useAuthStore } from '../../stores/authStore';
import { coursesAPI } from '../../services/api';

// Mock dependencies
vi.mock('../../stores/authStore');
vi.mock('../../services/api');
vi.mock('../../components/layout/SideNavBar', () => ({
  default: () => <div data-testid="side-navbar">SideNavBar</div>
}));

describe('Course CSS Consolidation', () => {
  const mockCurrentUser = {
    id: 'test-user',
    email: '<EMAIL>'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    useAuthStore.mockReturnValue({
      currentUser: mockCurrentUser,
      logout: vi.fn()
    });
    coursesAPI.getAll.mockResolvedValue({
      data: [
        {
          id: 1,
          title: 'Test Course',
          originalPrice: 1000,
          offerPrice: 800,
          status: 'Active',
          description: 'Test description',
          tags: ['test'],
          displayPicture: 'test.jpg',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ]
    });
  });

  it('should render Course page with consolidated CSS classes', async () => {
    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    // Check if main CSS classes are present
    const pageContainer = document.querySelector('.courses-page-container');
    expect(pageContainer).toBeInTheDocument();

    const headerCard = document.querySelector('.courses-header-card');
    expect(headerCard).toBeInTheDocument();

    const controlsRow = document.querySelector('.courses-controls-row');
    expect(controlsRow).toBeInTheDocument();

    const datatableWrapper = document.querySelector('.courses-datatable-wrapper');
    expect(datatableWrapper).toBeInTheDocument();
  });

  it('should have proper CSS class structure for table elements', async () => {
    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    // Wait for data to load and check table classes
    await screen.findByText('Test Course');

    const tableContainer = document.querySelector('.courses-table-container');
    expect(tableContainer).toBeInTheDocument();

    const table = document.querySelector('.courses-table');
    expect(table).toBeInTheDocument();
  });

  it('should apply correct styling without !important conflicts', () => {
    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    // Check that elements exist and can be styled
    const pageContainer = document.querySelector('.courses-page-container');
    expect(pageContainer).toHaveStyle('padding: 0');

    const headerCard = document.querySelector('.courses-header-card');
    expect(headerCard).toHaveStyle('background-color: #FFF8EB');
  });

  it('should maintain responsive design classes', () => {
    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    // Check that responsive classes are available
    const pageContainer = document.querySelector('.courses-page-container');
    expect(pageContainer).toBeInTheDocument();
    
    // The responsive styles are applied via CSS media queries
    // so we just verify the base elements exist
    expect(pageContainer).toHaveClass('courses-page-container');
  });
});
