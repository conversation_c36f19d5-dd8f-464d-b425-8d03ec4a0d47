/* DashboardMainContent Component Styles */
.dashboard-main-content {
  flex: 1;
  margin-left: 240px; /* Match sidebar width exactly */
  padding: 24px 32px; /* Consistent horizontal padding: 32px left and right */
  overflow-y: auto;
  background-color: #FFFBF3; /* Light cream main content background */
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Text Color Utilities */
.text-primary {
  color: var(--text-color);
}

.text-secondary {
  color: var(--secondary-text-color);
}

/* Courses Grid - Exactly 3 cards per row */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 0;
  margin: 0;
}

.course-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color, #007bff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.text-error {
  color: #dc3545;
  margin-bottom: 16px;
}

.retry-button {
  background-color: var(--primary-color, #007bff);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: var(--primary-color-dark, #0056b3);
}

/* Empty State */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}



/* Categories Section */
.categories-section {
  background-color: #FFFFFF; /* White background for content sections */
  border-radius: 12px;
  padding: 32px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* My Courses Section */
.my-courses-section {
  background-color: #FFFFFF; /* White background for content sections */
  border-radius: 12px;
  padding: 32px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-main-content {
    padding: 24px 24px; /* Reduce horizontal padding slightly for medium screens */
  }

  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-main-content {
    margin-left: 200px; /* Match reduced sidebar width at this breakpoint */
    padding: 24px 20px; /* Reduce horizontal padding for smaller screens */
  }

  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 640px) {
  .dashboard-main-content {
    margin-left: 0; /* Hide sidebar on mobile */
    padding: 16px; /* Equal padding on all sides for mobile */
  }

  .courses-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
